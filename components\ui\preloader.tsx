"use client"

import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from "lottie-react"

interface PreloaderProps {
  animationData?: any // Lottie animation JSON data
  duration?: number // Duration in milliseconds
  onComplete?: () => void
}

const Preloader: React.FC<PreloaderProps> = ({
  animationData,
  duration = 2000, // Match the provider duration
  onComplete
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [progress, setProgress] = useState(0)

  // Debug log to ensure preloader is being called
  console.log('Preloader rendered, isVisible:', isVisible, 'progress:', progress)

  useEffect(() => {
    console.log('Preloader useEffect started, duration:', duration)

    // Simplified progress animation
    let progressValue = 0
    const increment = 100 / (duration / 50) // 50ms intervals

    const progressInterval = setInterval(() => {
      progressValue += increment
      if (progressValue >= 100) {
        progressValue = 100
        clearInterval(progressInterval)
      }
      setProgress(Math.round(progressValue))
    }, 50)

    // Hide preloader after duration
    const timer = setTimeout(() => {
      console.log('Preloader timer completed, hiding preloader')
      setIsVisible(false)
      if (onComplete) {
        setTimeout(() => {
          console.log('Calling onComplete callback')
          onComplete()
        }, 500) // Wait for exit animation
      }
    }, duration)

    return () => {
      clearInterval(progressInterval)
      clearTimeout(timer)
    }
  }, [duration, onComplete])

  // Default animation data if none provided
  const defaultAnimationData = {
    v: "5.7.4",
    fr: 30,
    ip: 0,
    op: 90,
    w: 200,
    h: 200,
    nm: "Loading",
    ddd: 0,
    assets: [],
    layers: [
      {
        ddd: 0,
        ind: 1,
        ty: 4,
        nm: "Circle",
        sr: 1,
        ks: {
          o: { a: 0, k: 100 },
          r: {
            a: 1,
            k: [
              { i: { x: [0.833], y: [0.833] }, o: { x: [0.167], y: [0.167] }, t: 0, s: [0] },
              { t: 90, s: [360] }
            ]
          },
          p: { a: 0, k: [100, 100, 0] },
          a: { a: 0, k: [0, 0, 0] },
          s: { a: 0, k: [100, 100, 100] }
        },
        ao: 0,
        shapes: [
          {
            ty: "gr",
            it: [
              {
                d: 1,
                ty: "el",
                s: { a: 0, k: [60, 60] },
                p: { a: 0, k: [0, 0] }
              },
              {
                ty: "st",
                c: { a: 0, k: [0.722, 0.110, 0.361, 1] },
                o: { a: 0, k: 100 },
                w: { a: 0, k: 4 }
              },
              {
                ty: "tr",
                p: { a: 0, k: [0, 0] },
                a: { a: 0, k: [0, 0] },
                s: { a: 0, k: [100, 100] },
                r: { a: 0, k: 0 },
                o: { a: 0, k: 100 }
              }
            ]
          }
        ],
        ip: 0,
        op: 90,
        st: 0,
        bm: 0
      }
    ]
  }

  // Show preloader only when visible
  if (!isVisible) return null

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-white"
      style={{
        display: 'flex',
        visibility: 'visible',
        opacity: 1
      }}
    >
      <div className="flex flex-col items-center justify-center space-y-8">
        {/* Lottie Animation Only */}
        <div className="w-40 h-40 md:w-48 md:h-48 flex items-center justify-center">
          <Lottie
            animationData={animationData || defaultAnimationData}
            loop={true}
            autoplay={true}
            style={{ width: "100%", height: "100%" }}
            rendererSettings={{
              preserveAspectRatio: 'xMidYMid slice'
            }}
            onLoadedData={() => console.log('Lottie animation loaded successfully')}
          />
        </div>

        {/* Progress Bar */}
        <div className="relative w-[200px]">
          <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-burgundy-600 to-burgundy-500 rounded-full transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-center text-sm text-gray-600 mt-2">
            Loading... {progress}%
          </p>
        </div>

        {/* Loading Text */}
        <div className="text-center">
          <h2 className="text-xl font-playfair text-gray-800 mb-2">
            Welcome to Dwelling Desire
          </h2>
          <p className="text-sm text-gray-600">
            Your luxury real estate experience is loading...
          </p>
        </div>
      </div>
    </div>
  )
}

export default Preloader
